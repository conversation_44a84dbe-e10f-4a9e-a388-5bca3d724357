"""
IndexTTS 推理模块
该模块实现了 IndexTTS 的核心推理功能，包括文本处理、语音合成和音频输出
"""
import os  # 用于文件和目录操作
import sys  # 用于访问 Python 解释器变量
import time  # 用于性能计时
from subprocess import CalledProcessError  # 用于处理子进程调用错误
from typing import Dict, List, Tuple  # 用于类型提示

import torch  # PyTorch 深度学习框架
import torchaudio  # 音频处理库
from torch.nn.utils.rnn import pad_sequence  # 用于序列填充
from omegaconf import OmegaConf  # 配置文件管理器
from tqdm import tqdm  # 进度条显示

import warnings  # 警告处理模块

# 忽略特定警告，避免输出干扰信息
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# 导入项目模块
from indextts.BigVGAN.models import BigVGAN as Generator  # BigVGAN 声码器模型
from indextts.gpt.model import UnifiedVoice  # GPT 统一语音模型
from indextts.utils.checkpoint import load_checkpoint  # 模型检查点加载工具
from indextts.utils.feature_extractors import MelSpectrogramFeatures  # 梅尔频谱特征提取器

from indextts.utils.front import TextNormalizer, TextTokenizer  # 文本标准化和分词器


class IndexTTS:
    """
    IndexTTS 主类，封装了文本到语音合成的核心功能
    """
    def __init__(
        self, cfg_path="checkpoints/config.yaml", model_dir="checkpoints", is_fp16=True, device=None, use_cuda_kernel=None,
    ):
        """
        初始化IndexTTS模型
        Args:
            cfg_path (str): 配置文件路径
            model_dir (str): 模型目录路径
            is_fp16 (bool): 是否使用fp16精度
            device (str): 使用的设备 (例如 'cuda:0', 'cpu')。如果为None，则根据CUDA或MPS的可用性自动设置
            use_cuda_kernel (None | bool): 是否使用BigVGan自定义融合激活CUDA内核，仅适用于CUDA设备
        """
        # 根据设备可用性设置设备和精度参数
        if device is not None:
            self.device = device  # 使用指定设备
            self.is_fp16 = False if device == "cpu" else is_fp16  # CPU上不使用fp16
            self.use_cuda_kernel = use_cuda_kernel is not None and use_cuda_kernel and device.startswith("cuda")  # CUDA内核使用设置
        elif torch.cuda.is_available():  # 如果CUDA可用
            self.device = "cuda:0"  # 使用第一个GPU
            self.is_fp16 = is_fp16  # 设置精度
            self.use_cuda_kernel = use_cuda_kernel is None or use_cuda_kernel  # 使用CUDA内核
        elif hasattr(torch, "mps") and torch.backends.mps.is_available():  # 如果MPS可用（Apple Silicon）
            self.device = "mps"  # 使用MPS设备
            self.is_fp16 = False # 在MPS上使用float16比float32开销更大
            self.use_cuda_kernel = False  # MPS不支持CUDA内核
        else:  # 默认使用CPU
            self.device = "cpu"  # 使用CPU设备
            self.is_fp16 = False  # CPU上不使用fp16
            self.use_cuda_kernel = False  # CPU不支持CUDA内核
            print(">> Be patient, it may take a while to run in CPU mode.")  # 提示用户CPU运行较慢

        # 加载配置文件和设置基本参数
        self.cfg = OmegaConf.load(cfg_path)  # 加载配置文件
        self.model_dir = model_dir  # 设置模型目录
        self.dtype = torch.float16 if self.is_fp16 else None  # 设置数据类型
        self.stop_mel_token = self.cfg.gpt.stop_mel_token  # 获取停止标记

        # 加载GPT模型
        self.gpt = UnifiedVoice(**self.cfg.gpt)  # 初始化GPT模型
        self.gpt_path = os.path.join(self.model_dir, self.cfg.gpt_checkpoint)  # 构建GPT模型路径
        load_checkpoint(self.gpt, self.gpt_path)  # 加载GPT检查点
        self.gpt = self.gpt.to(self.device)  # 将模型移到指定设备
        if self.is_fp16:  # 如果使用fp16精度
            self.gpt.eval().half()  # 设置为评估模式并转换为半精度
        else:  # 如果使用fp32精度
            self.gpt.eval()  # 设置为评估模式
        print(">> GPT weights restored from:", self.gpt_path)  # 打印GPT模型加载信息
        if self.is_fp16:  # 如果使用fp16精度
            try:
                import deepspeed  # 尝试导入deepspeed
                use_deepspeed = True  # 成功导入则使用deepspeed
            except (ImportError, OSError, CalledProcessError) as e:  # 捕获导入错误
                use_deepspeed = False  # 不使用deepspeed
                print(f">> DeepSpeed加载失败，回退到标准推理: {e}")  # 打印错误信息
                print("See more details https://www.deepspeed.ai/tutorials/advanced-install/")  # 提供更多信息链接

            self.gpt.post_init_gpt2_config(use_deepspeed=use_deepspeed, kv_cache=True, half=True)  # 初始化GPT配置
        else:  # 如果不使用fp16精度
            self.gpt.post_init_gpt2_config(use_deepspeed=False, kv_cache=True, half=False)  # 初始化GPT配置

        # 加载BigVGAN CUDA内核（如果启用）
        if self.use_cuda_kernel:  # 如果启用CUDA内核
            # 预加载BigVGAN的CUDA内核
            try:
                from indextts.BigVGAN.alias_free_activation.cuda import load as anti_alias_activation_loader  # 导入CUDA加载器
                anti_alias_activation_cuda = anti_alias_activation_loader.load()  # 加载CUDA内核
                print(">> Preload custom CUDA kernel for BigVGAN", anti_alias_activation_cuda)  # 打印加载成功信息
            except Exception as e:  # 捕获加载异常
                print(">> Failed to load custom CUDA kernel for BigVGAN. Falling back to torch.", e, file=sys.stderr)  # 打印错误信息
                print(" Reinstall with `pip install -e . --no-deps --no-build-isolation` to prebuild `anti_alias_activation_cuda` kernel.", file=sys.stderr)  # 提供解决方案
                print(
                    "See more details: https://github.com/index-tts/index-tts/issues/164#issuecomment-2903453206", file=sys.stderr
                )  # 提供更多信息链接
                self.use_cuda_kernel = False  # 回退到PyTorch实现
        # 加载BigVGAN声码器
        self.bigvgan = Generator(self.cfg.bigvgan, use_cuda_kernel=self.use_cuda_kernel)  # 初始化BigVGAN声码器
        self.bigvgan_path = os.path.join(self.model_dir, self.cfg.bigvgan_checkpoint)  # 构建声码器模型路径
        vocoder_dict = torch.load(self.bigvgan_path, map_location="cpu")  # 加载声码器检查点
        self.bigvgan.load_state_dict(vocoder_dict["generator"])  # 加载生成器权重
        self.bigvgan = self.bigvgan.to(self.device)  # 将声码器移到指定设备
        # 移除eval模式下的权重归一化
        self.bigvgan.remove_weight_norm()  # 移除权重归一化以提高推理速度
        self.bigvgan.eval()  # 设置声码器为评估模式
        print(">> bigvgan weights restored from:", self.bigvgan_path)  # 打印声码器加载信息
        # 加载文本处理相关模块
        self.bpe_path = os.path.join(self.model_dir, self.cfg.dataset["bpe_model"])  # 构建BPE模型路径
        self.normalizer = TextNormalizer()  # 初始化文本标准化器
        self.normalizer.load()  # 加载文本标准化器
        print(">> TextNormalizer loaded")  # 打印文本标准化器加载信息
        self.tokenizer = TextTokenizer(self.bpe_path, self.normalizer)  # 初始化文本分词器
        print(">> bpe model loaded from:", self.bpe_path)  # 打印BPE模型加载信息
        # 缓存参考音频mel：
        self.cache_audio_prompt = None  # 缓存音频提示路径
        self.cache_cond_mel = None  # 缓存条件梅尔频谱
        # 进度引用显示（可选）
        self.gr_progress = None  # Gradio进度条引用
        self.model_version = self.cfg.version if hasattr(self.cfg, "version") else None  # 获取模型版本

    def remove_long_silence(self, codes: torch.Tensor, silent_token=52, max_consecutive=30):
        """
        缩短codes中的特殊标记（静音标记和停止mel标记）
        codes: [B, T]
        """
        code_lens = []  # 存储每个序列的有效长度
        codes_list = []  # 存储处理后的codes
        device = codes.device  # 获取张量所在的设备
        dtype = codes.dtype  # 获取张量的数据类型
        isfix = False  # 标记是否进行了修改
        for i in range(0, codes.shape[0]):  # 遍历batch中的每个序列
            code = codes[i]  # 获取当前序列
            # 确定有效长度（遇到stop_mel_token就停止）
            if not torch.any(code == self.stop_mel_token).item():  # 如果没有停止标记
                len_ = code.size(0)  # 序列长度为整个序列
            else:  # 如果有停止标记
                stop_mel_idx = (code == self.stop_mel_token).nonzero(as_tuple=False)  # 找到停止标记的位置
                len_ = stop_mel_idx[0].item() if len(stop_mel_idx) > 0 else code.size(0)  # 获取第一个停止标记位置

            # 统计静音标记数量
            count = torch.sum(code == silent_token).item()  # 统计静音标记的数量
            if count > max_consecutive:  # 如果静音标记数量超过阈值
                # 如果静音标记过多，需要进行缩减处理
                # code = code.cpu().tolist()
                ncode_idx = []  # 存储保留的索引
                n = 0  # 连续静音计数器
                for k in range(len_):  # 遍历序列
                    assert code[k] != self.stop_mel_token, f"stop_mel_token {self.stop_mel_token} should be shrinked here"  # 确保没有停止标记
                    if code[k] != silent_token:  # 如果不是静音标记
                        ncode_idx.append(k)  # 添加索引
                        n = 0  # 重置计数器
                    elif code[k] == silent_token and n < 10:  # 如果是静音标记且连续数量小于10
                        ncode_idx.append(k)  # 添加索引
                        n += 1  # 增加计数器
                # 新的code
                len_ = len(ncode_idx)  # 更新序列长度
                codes_list.append(code[ncode_idx])  # 添加处理后的序列
                isfix = True  # 标记已修改
            else:  # 如果静音标记数量未超过阈值
                # 缩减到len_长度
                codes_list.append(code[:len_])  # 添加截断后的序列
            code_lens.append(len_)  # 添加序列长度
        if isfix:  # 如果进行了修改
            if len(codes_list) > 1:  # 如果有多个序列
                codes = pad_sequence(codes_list, batch_first=True, padding_value=self.stop_mel_token)  # 填充序列
            else:  # 如果只有一个序列
                codes = codes_list[0].unsqueeze(0)  # 增加批次维度
        else:  # 如果未修改
            # 未改变
            pass
        # 裁剪codes到最大长度
        max_len = max(code_lens)  # 获取最大长度
        if max_len < codes.shape[1]:  # 如果最大长度小于当前长度
            codes = codes[:, :max_len]  # 裁剪到最大长度
        code_lens = torch.tensor(code_lens, dtype=torch.long, device=device)  # 转换为张量
        return codes, code_lens  # 返回处理后的codes和长度

    def bucket_sentences(self, sentences, bucket_max_size=4) -> List[List[Dict]]:
        """
        句子数据分桶处理
        如果 ``bucket_max_size=1``，将所有句子放在一个桶中
        """
        outputs: List[Dict] = []  # 存储处理后的句子
        for idx, sent in enumerate(sentences):  # 遍历句子
            outputs.append({"idx": idx, "sent": sent, "len": len(sent)})  # 添加句子信息
       
        if len(outputs) > bucket_max_size:  # 如果句子数量大于桶大小
            # 根据句子长度将句子拆分为多个桶
            buckets: List[List[Dict]] = []  # 存储桶
            factor = 1.5  # 长度因子
            last_bucket = None  # 上一个桶
            last_bucket_sent_len_median = 0  # 上一个桶的句子长度中位数

            for sent in sorted(outputs, key=lambda x: x["len"]):  # 按句子长度排序
                current_sent_len = sent["len"]  # 获取当前句子长度
                if current_sent_len == 0:  # 如果句子为空
                    print(">> skip empty sentence")  # 打印跳过信息
                    continue  # 跳过空句子
                if last_bucket is None \
                        or current_sent_len >= int(last_bucket_sent_len_median * factor) \
                        or len(last_bucket) >= bucket_max_size:  # 如果需要新建桶
                    # 新建桶
                    buckets.append([sent])  # 添加新桶
                    last_bucket = buckets[-1]  # 更新上一个桶
                    last_bucket_sent_len_median = current_sent_len  # 更新中位数
                else:  # 如果当前桶可以容纳更多句子
                    # 当前桶可以容纳更多句子
                    last_bucket.append(sent) # 排序
                    mid = len(last_bucket) // 2  # 计算中位数索引
                    last_bucket_sent_len_median = last_bucket[mid]["len"]  # 更新中位数
            last_bucket=None  # 清空上一个桶引用
            # 合并所有大小为1的桶
            out_buckets: List[List[Dict]] = []  # 存储输出桶
            only_ones: List[Dict] = []  # 存储大小为1的桶
            for b in buckets:  # 遍历所有桶
                if len(b) == 1:  # 如果桶大小为1
                    only_ones.append(b[0])  # 添加到only_ones
                else:  # 如果桶大小大于1
                    out_buckets.append(b)  # 添加到输出桶
            if len(only_ones) > 0:  # 如果有大小为1的桶
                # 如果可能，合并到之前的桶中
                # print("only_ones:", [(o["idx"], o["len"]) for o in only_ones])
                for i in range(len(out_buckets)):  # 遍历输出桶
                    b = out_buckets[i]  # 获取当前桶
                    if len(b) < bucket_max_size:  # 如果桶未满
                        b.append(only_ones.pop(0))  # 添加元素
                        if len(only_ones) == 0:  # 如果没有元素了
                            break  # 退出循环
                # 合并所有剩余的大小为1的桶
                if len(only_ones) > 0:  # 如果还有剩余元素
                    out_buckets.extend([only_ones[i:i+bucket_max_size] for i in range(0, len(only_ones), bucket_max_size)])  # 合并剩余元素
            return out_buckets  # 返回分桶结果
        return [outputs]  # 返回单个桶

    def pad_tokens_cat(self, tokens: List[torch.Tensor]) -> torch.Tensor:
        # 1.5版本以上，直接使用stop_text_token右侧填充，填充到最大长度
        if self.model_version and self.model_version >= 1.5:  # 如果模型版本大于等于1.5
            # [1, N] -> [N,]
            tokens = [t.squeeze(0) for t in tokens]  # 去除批次维度
            return pad_sequence(tokens, batch_first=True, padding_value=self.cfg.gpt.stop_text_token, padding_side="right")  # 右侧填充
        max_len = max(t.size(1) for t in tokens)  # 获取最大长度
        outputs = []  # 存储处理后的tokens
        for tensor in tokens:  # 遍历tokens
            pad_len = max_len - tensor.size(1)  # 计算需要填充的长度
            if pad_len > 0:  # 如果需要填充
                n = min(8, pad_len)  # 计算第一部分填充长度
                tensor = torch.nn.functional.pad(tensor, (0, n), value=self.cfg.gpt.stop_text_token)  # 填充stop_text_token
                tensor = torch.nn.functional.pad(tensor, (0, pad_len - n), value=self.cfg.gpt.start_text_token)  # 填充start_text_token
            tensor = tensor[:, :max_len]  # 裁剪到最大长度
            outputs.append(tensor)  # 添加到输出
        tokens = torch.cat(outputs, dim=0)  # 拼接张量
        return tokens  # 返回处理后的tokens

    def torch_empty_cache(self):
        # 清空GPU或MPS缓存
        try:
            if "cuda" in str(self.device):  # 如果使用CUDA设备
                torch.cuda.empty_cache()  # 清空CUDA缓存
            elif "mps" in str(self.device):  # 如果使用MPS设备
                torch.mps.empty_cache()  # 清空MPS缓存
        except Exception as e:  # 捕获异常
            pass  # 忽略异常

    def _set_gr_progress(self, value, desc):
        # 设置gradio进度条（如果可用）
        if self.gr_progress is not None:  # 如果进度条引用存在
            self.gr_progress(value, desc=desc)  # 设置进度条

    # 快速推理：对于"多句长文本"，可实现至少 2~10 倍以上的速度提升~ （First modified by sunnyboxs 2025-04-16）
    def infer_fast(self, audio_prompt, text, output_path, verbose=False, max_text_tokens_per_sentence=100, sentences_bucket_max_size=4, **generation_kwargs):
        """
        快速推理方法
        Args:
            ``max_text_tokens_per_sentence``: 分句的最大token数，默认``100``，可以根据GPU硬件情况调整
                - 越小，batch 越多，推理速度越*快*，占用内存更多，可能影响质量
                - 越大，batch 越少，推理速度越*慢*，占用内存和质量更接近于非快速推理
            ``sentences_bucket_max_size``: 分句分桶的最大容量，默认``4``，可以根据GPU内存调整
                - 越大，bucket数量越少，batch越多，推理速度越*快*，占用内存更多，可能影响质量
                - 越小，bucket数量越多，batch越少，推理速度越*慢*，占用内存和质量更接近于非快速推理
        """
        print(">> start fast inference...")  # 打印开始快速推理信息
        
        self._set_gr_progress(0, "start fast inference...")  # 设置进度条
        if verbose:  # 如果启用详细输出
            print(f"origin text:{text}")  # 打印原始文本
        start_time = time.perf_counter()  # 记录开始时间

        # 如果参考音频改变了，才需要重新生成 cond_mel, 提升速度
        if self.cache_cond_mel is None or self.cache_audio_prompt != audio_prompt:  # 如果缓存不存在或音频提示改变
            audio, sr = torchaudio.load(audio_prompt)  # 加载音频文件
            audio = torch.mean(audio, dim=0, keepdim=True)  # 转换为单声道
            if audio.shape[0] > 1:  # 如果声道数大于1
                audio = audio[0].unsqueeze(0)  # 只保留第一个声道
            audio = torchaudio.transforms.Resample(sr, 24000)(audio)  # 重采样到24kHz
            cond_mel = MelSpectrogramFeatures()(audio).to(self.device)  # 提取梅尔频谱特征
            cond_mel_frame = cond_mel.shape[-1]  # 获取帧数
            if verbose:  # 如果启用详细输出
                print(f"cond_mel shape: {cond_mel.shape}", "dtype:", cond_mel.dtype)  # 打印梅尔频谱信息

            self.cache_audio_prompt = audio_prompt  # 更新缓存音频提示
            self.cache_cond_mel = cond_mel  # 更新缓存条件梅尔频谱
        else:  # 如果缓存存在且音频提示未改变
            cond_mel = self.cache_cond_mel  # 使用缓存的条件梅尔频谱
            cond_mel_frame = cond_mel.shape[-1]  # 获取帧数
            pass  # 跳过处理

        auto_conditioning = cond_mel  # 设置自动条件
        cond_mel_lengths = torch.tensor([cond_mel_frame], device=self.device)  # 创建条件梅尔频谱长度张量

        # 文本分词
        text_tokens_list = self.tokenizer.tokenize(text)  # 对文本进行分词

        sentences = self.tokenizer.split_sentences(text_tokens_list, max_tokens_per_sentence=max_text_tokens_per_sentence)  # 分割句子
        if verbose:  # 如果启用详细输出
            print(">> text token count:", len(text_tokens_list))  # 打印文本token数量
            print("   splited sentences count:", len(sentences))  # 打印分割句子数量
            print("   max_text_tokens_per_sentence:", max_text_tokens_per_sentence)  # 打印每句最大token数
            print(*sentences, sep="\n")  # 打印分割后的句子
        # 设置生成参数
        do_sample = generation_kwargs.pop("do_sample", True)  # 是否采样
        top_p = generation_kwargs.pop("top_p", 0.8)  # top-p采样参数
        top_k = generation_kwargs.pop("top_k", 30)  # top-k采样参数
        temperature = generation_kwargs.pop("temperature", 1.0)  # 温度参数
        autoregressive_batch_size = 1  # 自回归批次大小
        length_penalty = generation_kwargs.pop("length_penalty", 0.0)  # 长度惩罚
        num_beams = generation_kwargs.pop("num_beams", 3)  # beam search数量
        repetition_penalty = generation_kwargs.pop("repetition_penalty", 10.0)  # 重复惩罚
        max_mel_tokens = generation_kwargs.pop("max_mel_tokens", 600)  # 最大mel token数
        sampling_rate = 24000  # 采样率
        # lang = "EN"
        # lang = "ZH"
        wavs = []  # 存储生成的音频波形
        gpt_gen_time = 0  # GPT生成时间
        gpt_forward_time = 0  # GPT前向传播时间
        bigvgan_time = 0  # BigVGAN生成时间

        # 文本处理
        all_text_tokens: List[List[torch.Tensor]] = []  # 存储所有文本tokens
        self._set_gr_progress(0.1, "text processing...")  # 设置进度条
        bucket_max_size = sentences_bucket_max_size if self.device != "cpu" else 1  # 设置桶大小
        all_sentences = self.bucket_sentences(sentences, bucket_max_size=bucket_max_size)  # 分桶处理句子
        bucket_count = len(all_sentences)  # 获取桶数量
        if verbose:  # 如果启用详细输出
            print(">> sentences bucket_count:", bucket_count,  # 打印桶数量
                  "bucket sizes:", [(len(s), [t["idx"] for t in s]) for s in all_sentences],  # 打印桶大小
                  "bucket_max_size:", bucket_max_size)  # 打印桶最大大小
        for sentences in all_sentences:  # 遍历所有桶
            temp_tokens: List[torch.Tensor] = []  # 临时存储tokens
            all_text_tokens.append(temp_tokens)  # 添加到所有文本tokens
            for item in sentences:  # 遍历桶中的句子
                sent = item["sent"]  # 获取句子
                text_tokens = self.tokenizer.convert_tokens_to_ids(sent)  # 转换tokens为ID
                text_tokens = torch.tensor(text_tokens, dtype=torch.int32, device=self.device).unsqueeze(0)  # 转换为张量
                if verbose:  # 如果启用详细输出
                    print(text_tokens)  # 打印文本tokens
                    print(f"text_tokens shape: {text_tokens.shape}, text_tokens type: {text_tokens.dtype}")  # 打印文本tokens信息
                    # debug tokenizer
                    text_token_syms = self.tokenizer.convert_ids_to_tokens(text_tokens[0].tolist())  # 转换ID为tokens
                    print("text_token_syms is same as sentence tokens", text_token_syms == sent)  # 检查转换是否正确
                temp_tokens.append(text_tokens)  # 添加到临时tokens
        
            
        # 顺序处理分桶数据
        all_batch_num = sum(len(s) for s in all_sentences)  # 计算总批次数
        all_batch_codes = []  # 存储所有批处理codes
        processed_num = 0  # 已处理数量
        for item_tokens in all_text_tokens:  # 遍历所有文本tokens
            batch_num = len(item_tokens)  # 获取批次大小
            if batch_num > 1:  # 如果批次大小大于1
                batch_text_tokens = self.pad_tokens_cat(item_tokens)  # 填充tokens
            else:  # 如果批次大小为1
                batch_text_tokens = item_tokens[0]  # 直接获取tokens
            processed_num += batch_num  # 更新已处理数量
            # gpt speech
            self._set_gr_progress(0.2 + 0.3 * processed_num/all_batch_num, f"gpt inference speech... {processed_num}/{all_batch_num}")  # 设置进度条
            m_start_time = time.perf_counter()  # 记录开始时间
            with torch.no_grad():  # 禁用梯度计算
                with torch.amp.autocast(batch_text_tokens.device.type, enabled=self.dtype is not None, dtype=self.dtype):  # 自动混合精度
                    temp_codes = self.gpt.inference_speech(auto_conditioning, batch_text_tokens,
                                        cond_mel_lengths=cond_mel_lengths,
                                        # text_lengths=text_len,
                                        do_sample=do_sample,
                                        top_p=top_p,
                                        top_k=top_k,
                                        temperature=temperature,
                                        num_return_sequences=autoregressive_batch_size,
                                        length_penalty=length_penalty,
                                        num_beams=num_beams,
                                        repetition_penalty=repetition_penalty,
                                        max_generate_length=max_mel_tokens,
                                        **generation_kwargs)  # GPT推理语音
                    all_batch_codes.append(temp_codes)  # 添加到批处理codes
            gpt_gen_time += time.perf_counter() - m_start_time  # 更新GPT生成时间

        # gpt latent
        self._set_gr_progress(0.5, "gpt inference latents...")  # 设置进度条
        all_idxs = []  # 存储索引
        all_latents = []  # 存储潜在表示
        has_warned = False  # 警告标记
        for batch_codes, batch_tokens, batch_sentences in zip(all_batch_codes, all_text_tokens, all_sentences):  # 遍历批处理数据
            for i in range(batch_codes.shape[0]):  # 遍历批次中的每个元素
                codes = batch_codes[i]  # 获取codes
                if not has_warned and codes[-1] != self.stop_mel_token:  # 如果未警告且最后一个token不是停止标记
                    warnings.warn(
                        f"WARN: generation stopped due to exceeding `max_mel_tokens` ({max_mel_tokens}). "
                        f"Consider reducing `max_text_tokens_per_sentence`({max_text_tokens_per_sentence}) or increasing `max_mel_tokens`.",
                        category=RuntimeWarning
                    )  # 发出警告
                    has_warned = True  # 设置警告标记
                codes = codes.unsqueeze(0)  # 增加维度
                if verbose:  # 如果启用详细输出
                    print("codes:", codes.shape)  # 打印codes形状
                    print(codes)  # 打印codes
                codes, code_lens = self.remove_long_silence(codes, silent_token=52, max_consecutive=30)  # 移除长静音
                if verbose:  # 如果启用详细输出
                    print("fix codes:", codes.shape)  # 打印修复后的codes形状
                    print(codes)  # 打印修复后的codes
                    print("code_lens:", code_lens)  # 打印codes长度
                text_tokens = batch_tokens[i]  # 获取文本tokens
                all_idxs.append(batch_sentences[i]["idx"])  # 添加索引
                m_start_time = time.perf_counter()  # 记录开始时间
                with torch.no_grad():  # 禁用梯度计算
                    with torch.amp.autocast(text_tokens.device.type, enabled=self.dtype is not None, dtype=self.dtype):  # 自动混合精度
                        latent = \
                            self.gpt(auto_conditioning, text_tokens,
                                        torch.tensor([text_tokens.shape[-1]], device=text_tokens.device), codes,
                                        code_lens*self.gpt.mel_length_compression,
                                        cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]], device=text_tokens.device),
                                        return_latent=True, clip_inputs=False)  # GPT前向传播获取潜在表示
                        gpt_forward_time += time.perf_counter() - m_start_time  # 更新GPT前向传播时间
                        all_latents.append(latent)  # 添加潜在表示
        del all_batch_codes, all_text_tokens, all_sentences  # 删除临时变量释放内存
        # bigvgan chunk
        chunk_size = 2  # 块大小
        all_latents = [all_latents[all_idxs.index(i)] for i in range(len(all_latents))]  # 重新排序潜在表示
        if verbose:  # 如果启用详细输出
            print(">> all_latents:", len(all_latents))  # 打印潜在表示数量
            print("  latents length:", [l.shape[1] for l in all_latents])  # 打印潜在表示长度
        chunk_latents = [all_latents[i : i + chunk_size] for i in range(0, len(all_latents), chunk_size)]  # 分块处理潜在表示
        chunk_length = len(chunk_latents)  # 获取块数量
        latent_length = len(all_latents)  # 获取潜在表示数量

        # bigvgan chunk decode
        self._set_gr_progress(0.7, "bigvgan decode...")  # 设置进度条
        tqdm_progress = tqdm(total=latent_length, desc="bigvgan")  # 创建进度条
        for items in chunk_latents:  # 遍历块
            tqdm_progress.update(len(items))  # 更新进度条
            latent = torch.cat(items, dim=1)  # 拼接潜在表示
            with torch.no_grad():  # 禁用梯度计算
                with torch.amp.autocast(latent.device.type, enabled=self.dtype is not None, dtype=self.dtype):  # 自动混合精度
                    m_start_time = time.perf_counter()  # 记录开始时间
                    wav, _ = self.bigvgan(latent, auto_conditioning.transpose(1, 2))  # BigVGAN解码
                    bigvgan_time += time.perf_counter() - m_start_time  # 更新BigVGAN时间
                    wav = wav.squeeze(1)  # 去除维度
                    pass  # 占位符
            wav = torch.clamp(32767 * wav, -32767.0, 32767.0)  # 限制波形幅度
            wavs.append(wav.cpu()) # to cpu before saving  # 移动到CPU并添加到波形列表

        # clear cache
        tqdm_progress.close()  # 确保进度条被关闭
        del all_latents, chunk_latents  # 删除临时变量释放内存
        end_time = time.perf_counter()  # 记录结束时间
        self.torch_empty_cache()  # 清空缓存

        # wav audio output
        self._set_gr_progress(0.9, "save audio...")  # 设置进度条
        wav = torch.cat(wavs, dim=1)  # 拼接所有波形
        wav_length = wav.shape[-1] / sampling_rate  # 计算音频长度
        print(f">> Reference audio length: {cond_mel_frame * 256 / sampling_rate:.2f} seconds")  # 打印参考音频长度
        print(f">> gpt_gen_time: {gpt_gen_time:.2f} seconds")  # 打印GPT生成时间
        print(f">> gpt_forward_time: {gpt_forward_time:.2f} seconds")  # 打印GPT前向传播时间
        print(f">> bigvgan_time: {bigvgan_time:.2f} seconds")  # 打印BigVGAN时间
        print(f">> Total fast inference time: {end_time - start_time:.2f} seconds")  # 打印总推理时间
        print(f">> Generated audio length: {wav_length:.2f} seconds")  # 打印生成音频长度
        print(f">> [fast] bigvgan chunk_length: {chunk_length}")  # 打印BigVGAN块数量
        print(f">> [fast] batch_num: {all_batch_num} bucket_max_size: {bucket_max_size}", f"bucket_count: {bucket_count}" if bucket_max_size > 1 else "")  # 打印批次信息
        print(f">> [fast] RTF: {(end_time - start_time) / wav_length:.4f}")  # 打印实时因子

        # save audio
        wav = wav.cpu()  # to cpu  # 移动到CPU
        if output_path:  # 如果指定了输出路径
            # 直接保存音频到指定路径中
            os.makedirs(os.path.dirname(output_path), exist_ok=True)  # 创建目录
            torchaudio.save(output_path, wav.type(torch.int16), sampling_rate)  # 保存音频
            print(">> wav file saved to:", output_path)  # 打印保存信息
            return output_path  # 返回输出路径
        else:  # 如果未指定输出路径
            # 返回以符合Gradio的格式要求
            wav_data = wav.type(torch.int16)  # 转换数据类型
            wav_data = wav_data.numpy().T  # 转换为numpy数组并转置
            return (sampling_rate, wav_data)  # 返回采样率和波形数据

    # 原始推理模式
    def infer(self, audio_prompt, text, output_path, verbose=False, max_text_tokens_per_sentence=120, **generation_kwargs):
        """
        原始推理方法
        """
        print(">> start inference...")  # 打印开始推理信息
        self._set_gr_progress(0, "start inference...")  # 设置进度条
        if verbose:  # 如果启用详细输出
            print(f"origin text:{text}")  # 打印原始文本
        start_time = time.perf_counter()  # 记录开始时间

        # 如果参考音频改变了，才需要重新生成 cond_mel, 提升速度
        if self.cache_cond_mel is None or self.cache_audio_prompt != audio_prompt:  # 如果缓存不存在或音频提示改变
            audio, sr = torchaudio.load(audio_prompt)  # 加载音频文件
            audio = torch.mean(audio, dim=0, keepdim=True)  # 转换为单声道
            if audio.shape[0] > 1:  # 如果声道数大于1
                audio = audio[0].unsqueeze(0)  # 只保留第一个声道
            audio = torchaudio.transforms.Resample(sr, 24000)(audio)  # 重采样到24kHz
            cond_mel = MelSpectrogramFeatures()(audio).to(self.device)  # 提取梅尔频谱特征
            cond_mel_frame = cond_mel.shape[-1]  # 获取帧数
            if verbose:  # 如果启用详细输出
                print(f"cond_mel shape: {cond_mel.shape}", "dtype:", cond_mel.dtype)  # 打印梅尔频谱信息

            self.cache_audio_prompt = audio_prompt  # 更新缓存音频提示
            self.cache_cond_mel = cond_mel  # 更新缓存条件梅尔频谱
        else:  # 如果缓存存在且音频提示未改变
            cond_mel = self.cache_cond_mel  # 使用缓存的条件梅尔频谱
            cond_mel_frame = cond_mel.shape[-1]  # 获取帧数
            pass  # 跳过处理

        self._set_gr_progress(0.1, "text processing...")  # 设置进度条
        auto_conditioning = cond_mel  # 设置自动条件
        text_tokens_list = self.tokenizer.tokenize(text)  # 对文本进行分词
        sentences = self.tokenizer.split_sentences(text_tokens_list, max_text_tokens_per_sentence)  # 分割句子
        if verbose:  # 如果启用详细输出
            print("text token count:", len(text_tokens_list))  # 打印文本token数量
            print("sentences count:", len(sentences))  # 打印句子数量
            print("max_text_tokens_per_sentence:", max_text_tokens_per_sentence)  # 打印每句最大token数
            print(*sentences, sep="\n")  # 打印分割后的句子
        # 设置生成参数
        do_sample = generation_kwargs.pop("do_sample", True)  # 是否采样
        top_p = generation_kwargs.pop("top_p", 0.8)  # top-p采样参数
        top_k = generation_kwargs.pop("top_k", 30)  # top-k采样参数
        temperature = generation_kwargs.pop("temperature", 1.0)  # 温度参数
        autoregressive_batch_size = 1  # 自回归批次大小
        length_penalty = generation_kwargs.pop("length_penalty", 0.0)  # 长度惩罚
        num_beams = generation_kwargs.pop("num_beams", 3)  # beam search数量
        repetition_penalty = generation_kwargs.pop("repetition_penalty", 10.0)  # 重复惩罚
        max_mel_tokens = generation_kwargs.pop("max_mel_tokens", 600)  # 最大mel token数
        sampling_rate = 24000  # 采样率
        # lang = "EN"
        # lang = "ZH"
        wavs = []  # 存储生成的音频波形
        gpt_gen_time = 0  # GPT生成时间
        gpt_forward_time = 0  # GPT前向传播时间
        bigvgan_time = 0  # BigVGAN生成时间
        progress = 0  # 进度计数器
        has_warned = False  # 警告标记
        for sent in sentences:  # 遍历句子
            text_tokens = self.tokenizer.convert_tokens_to_ids(sent)  # 转换tokens为ID
            text_tokens = torch.tensor(text_tokens, dtype=torch.int32, device=self.device).unsqueeze(0)  # 转换为张量
            # text_tokens = F.pad(text_tokens, (0, 1))  # This may not be necessary.
            # text_tokens = F.pad(text_tokens, (1, 0), value=0)
            # text_tokens = F.pad(text_tokens, (0, 1), value=1)
            if verbose:  # 如果启用详细输出
                print(text_tokens)  # 打印文本tokens
                print(f"text_tokens shape: {text_tokens.shape}, text_tokens type: {text_tokens.dtype}")  # 打印文本tokens信息
                # debug tokenizer
                text_token_syms = self.tokenizer.convert_ids_to_tokens(text_tokens[0].tolist())  # 转换ID为tokens
                print("text_token_syms is same as sentence tokens", text_token_syms == sent)  # 检查转换是否正确

            # text_len = torch.IntTensor([text_tokens.size(1)], device=text_tokens.device)
            # print(text_len)
            progress += 1  # 更新进度
            self._set_gr_progress(0.2 + 0.4 * (progress-1) / len(sentences), f"gpt inference latent... {progress}/{len(sentences)}")  # 设置进度条
            m_start_time = time.perf_counter()  # 记录开始时间
            with torch.no_grad():  # 禁用梯度计算
                with torch.amp.autocast(text_tokens.device.type, enabled=self.dtype is not None, dtype=self.dtype):  # 自动混合精度
                    codes = self.gpt.inference_speech(auto_conditioning, text_tokens,
                                                        cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]],
                                                                                      device=text_tokens.device),
                                                        # text_lengths=text_len,
                                                        do_sample=do_sample,
                                                        top_p=top_p,
                                                        top_k=top_k,
                                                        temperature=temperature,
                                                        num_return_sequences=autoregressive_batch_size,
                                                        length_penalty=length_penalty,
                                                        num_beams=num_beams,
                                                        repetition_penalty=repetition_penalty,
                                                        max_generate_length=max_mel_tokens,
                                                        **generation_kwargs)  # GPT推理语音
                gpt_gen_time += time.perf_counter() - m_start_time  # 更新GPT生成时间
                if not has_warned and (codes[:, -1] != self.stop_mel_token).any():  # 如果未警告且存在非停止标记
                    warnings.warn(
                        f"WARN: generation stopped due to exceeding `max_mel_tokens` ({max_mel_tokens}). "
                        f"Input text tokens: {text_tokens.shape[1]}. "
                        f"Consider reducing `max_text_tokens_per_sentence`({max_text_tokens_per_sentence}) or increasing `max_mel_tokens`.",
                        category=RuntimeWarning
                    )  # 发出警告
                    has_warned = True  # 设置警告标记

                code_lens = torch.tensor([codes.shape[-1]], device=codes.device, dtype=codes.dtype)  # 创建codes长度张量
                if verbose:  # 如果启用详细输出
                    print(codes, type(codes))  # 打印codes和类型
                    print(f"codes shape: {codes.shape}, codes type: {codes.dtype}")  # 打印codes信息
                    print(f"code len: {code_lens}")  # 打印codes长度

                # remove ultra-long silence if exits
                # temporarily fix the long silence bug.
                codes, code_lens = self.remove_long_silence(codes, silent_token=52, max_consecutive=30)  # 移除长静音
                if verbose:  # 如果启用详细输出
                    print(codes, type(codes))  # 打印codes和类型
                    print(f"fix codes shape: {codes.shape}, codes type: {codes.dtype}")  # 打印修复后的codes信息
                    print(f"code len: {code_lens}")  # 打印codes长度
                self._set_gr_progress(0.2 + 0.4 * progress / len(sentences), f"gpt inference speech... {progress}/{len(sentences)}")  # 设置进度条
                m_start_time = time.perf_counter()  # 记录开始时间
                # latent, text_lens_out, code_lens_out = \
                with torch.amp.autocast(text_tokens.device.type, enabled=self.dtype is not None, dtype=self.dtype):  # 自动混合精度
                    latent = \
                        self.gpt(auto_conditioning, text_tokens,
                                    torch.tensor([text_tokens.shape[-1]], device=text_tokens.device), codes,
                                    code_lens*self.gpt.mel_length_compression,
                                    cond_mel_lengths=torch.tensor([auto_conditioning.shape[-1]], device=text_tokens.device),
                                    return_latent=True, clip_inputs=False)  # GPT前向传播获取潜在表示
                    gpt_forward_time += time.perf_counter() - m_start_time  # 更新GPT前向传播时间

                    m_start_time = time.perf_counter()  # 记录开始时间
                    wav, _ = self.bigvgan(latent, auto_conditioning.transpose(1, 2))  # BigVGAN解码
                    bigvgan_time += time.perf_counter() - m_start_time  # 更新BigVGAN时间
                    wav = wav.squeeze(1)  # 去除维度

                wav = torch.clamp(32767 * wav, -32767.0, 32767.0)  # 限制波形幅度
                if verbose:  # 如果启用详细输出
                    print(f"wav shape: {wav.shape}", "min:", wav.min(), "max:", wav.max())  # 打印波形信息
                # wavs.append(wav[:, :-512])
                wavs.append(wav.cpu())  # to cpu before saving  # 移动到CPU并添加到波形列表
        end_time = time.perf_counter()  # 记录结束时间
        self._set_gr_progress(0.9, "save audio...")  # 设置进度条
        wav = torch.cat(wavs, dim=1)  # 拼接所有波形
        wav_length = wav.shape[-1] / sampling_rate  # 计算音频长度
        print(f">> Reference audio length: {cond_mel_frame * 256 / sampling_rate:.2f} seconds")  # 打印参考音频长度
        print(f">> gpt_gen_time: {gpt_gen_time:.2f} seconds")  # 打印GPT生成时间
        print(f">> gpt_forward_time: {gpt_forward_time:.2f} seconds")  # 打印GPT前向传播时间
        print(f">> bigvgan_time: {bigvgan_time:.2f} seconds")  # 打印BigVGAN时间
        print(f">> Total inference time: {end_time - start_time:.2f} seconds")  # 打印总推理时间
        print(f">> Generated audio length: {wav_length:.2f} seconds")  # 打印生成音频长度
        print(f">> RTF: {(end_time - start_time) / wav_length:.4f}")  # 打印实时因子

        # save audio
        wav = wav.cpu()  # to cpu  # 移动到CPU
        if output_path:  # 如果指定了输出路径
            # 直接保存音频到指定路径中
            if os.path.isfile(output_path):  # 如果文件已存在
                os.remove(output_path)  # 删除旧文件
                print(">> remove old wav file:", output_path)  # 打印删除信息
            if os.path.dirname(output_path) != "":  # 如果目录不为空
                os.makedirs(os.path.dirname(output_path), exist_ok=True)  # 创建目录
            torchaudio.save(output_path, wav.type(torch.int16), sampling_rate)  # 保存音频
            print(">> wav file saved to:", output_path)  # 打印保存信息
            return output_path  # 返回输出路径
        else:  # 如果未指定输出路径
            # 返回以符合Gradio的格式要求
            wav_data = wav.type(torch.int16)  # 转换数据类型
            wav_data = wav_data.numpy().T  # 转换为numpy数组并转置
            return (sampling_rate, wav_data)  # 返回采样率和波形数据


if __name__ == "__main__":
    prompt_wav="test_data/input.wav"  # 参考音频路径
    #text="晕 XUAN4 是 一 种 GAN3 觉"
    #text='大家好，我现在正在bilibili 体验 ai 科技，说实话，来之前我绝对想不到！AI技术已经发展到这样匪夷所思的地步了！'
    text="There is a vehicle arriving in dock number 7?"  # 测试文本

    tts = IndexTTS(cfg_path="checkpoints/config.yaml", model_dir="checkpoints", is_fp16=True, use_cuda_kernel=False)  # 初始化IndexTTS
    tts.infer(audio_prompt=prompt_wav, text=text, output_path="gen.wav", verbose=True)  # 执行推理
```